from flask import Flask, render_template, request, redirect, url_for, send_from_directory
from flask_babel import Babel
from flask_frozen import Freezer

app = Flask(__name__)
babel = Babel(app)
freezer = Freezer(app)

app.config['FREEZER_DESTINATION'] = 'build'


@app.route('/')
def index():
    return render_template('index.html')


@app.route('/permit-test-practice')
def permit_test_practice():
    return render_template('permit-test-practice.html')


@app.route('/dmv-permit-test')
def dmv_permit_test():
    return render_template('dmv-permit-test.html')


@app.route('/online-permit-test')
def online_permit_test():
    return render_template('online-permit-test.html')


@app.route('/practice-questions')
def practice_questions():
    return render_template('practice-questions.html')


@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404


if __name__ == '__main__':
    app.run(debug=True)
